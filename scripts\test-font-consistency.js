#!/usr/bin/env node

/**
 * 字体一致性测试脚本
 * 验证字体加载和预览一致性
 */

const fs = require('fs');
const path = require('path');

// 测试数据
const testData = {
  recipientName: '<PERSON>',
  details: 'For outstanding achievement in web development',
  date: 'December 25, 2024',
  signature: '<PERSON>'
};

// 检查字体文件是否存在
function checkFontFiles() {
  console.log('🔍 检查字体配置...');
  
  const fontLoaderPath = path.join(__dirname, '../src/lib/font-loader.ts');
  const fontsPath = path.join(__dirname, '../src/lib/fonts.ts');
  const layoutPath = path.join(__dirname, '../src/app/layout.tsx');
  
  const files = [
    { path: fontLoaderPath, name: 'FontLoader' },
    { path: fontsPath, name: 'Fonts配置' },
    { path: layoutPath, name: 'Layout配置' }
  ];
  
  let allExist = true;
  
  files.forEach(file => {
    if (fs.existsSync(file.path)) {
      console.log(`✅ ${file.name}: 存在`);
    } else {
      console.log(`❌ ${file.name}: 不存在`);
      allExist = false;
    }
  });
  
  return allExist;
}

// 检查字体映射配置
function checkFontMapping() {
  console.log('\n🔍 检查字体映射配置...');
  
  try {
    const fontsContent = fs.readFileSync(
      path.join(__dirname, '../src/lib/fonts.ts'), 
      'utf8'
    );
    
    const requiredFonts = [
      'Dancing Script',
      'Playfair Display',
      'Inter',
      'Crimson Text',
      'Source Sans Pro',
      'Great Vibes'
    ];
    
    let allMapped = true;
    
    requiredFonts.forEach(font => {
      if (fontsContent.includes(font)) {
        console.log(`✅ ${font}: 已配置`);
      } else {
        console.log(`❌ ${font}: 未配置`);
        allMapped = false;
      }
    });
    
    return allMapped;
  } catch (error) {
    console.error('❌ 读取字体配置文件失败:', error.message);
    return false;
  }
}

// 检查next/font配置
function checkNextFontConfig() {
  console.log('\n🔍 检查next/font配置...');
  
  try {
    const layoutContent = fs.readFileSync(
      path.join(__dirname, '../src/app/layout.tsx'), 
      'utf8'
    );
    
    const nextFontImports = [
      'Dancing_Script',
      'Playfair_Display',
      'Crimson_Text',
      'Source_Sans_Pro',
      'Great_Vibes'
    ];
    
    let allConfigured = true;
    
    nextFontImports.forEach(fontImport => {
      if (layoutContent.includes(fontImport)) {
        console.log(`✅ ${fontImport}: 已导入`);
      } else {
        console.log(`❌ ${fontImport}: 未导入`);
        allConfigured = false;
      }
    });
    
    // 检查CSS变量
    if (layoutContent.includes('--font-dancing-script')) {
      console.log('✅ CSS变量: 已配置');
    } else {
      console.log('❌ CSS变量: 未配置');
      allConfigured = false;
    }
    
    return allConfigured;
  } catch (error) {
    console.error('❌ 读取Layout配置文件失败:', error.message);
    return false;
  }
}

// 检查Tailwind配置
function checkTailwindConfig() {
  console.log('\n🔍 检查Tailwind配置...');
  
  try {
    const tailwindContent = fs.readFileSync(
      path.join(__dirname, '../tailwind.config.ts'), 
      'utf8'
    );
    
    const cssVariables = [
      '--font-dancing-script',
      '--font-playfair-display',
      '--font-crimson-text',
      '--font-source-sans-pro',
      '--font-great-vibes'
    ];
    
    let allConfigured = true;
    
    cssVariables.forEach(variable => {
      if (tailwindContent.includes(variable)) {
        console.log(`✅ ${variable}: 已配置`);
      } else {
        console.log(`❌ ${variable}: 未配置`);
        allConfigured = false;
      }
    });
    
    return allConfigured;
  } catch (error) {
    console.error('❌ 读取Tailwind配置文件失败:', error.message);
    return false;
  }
}

// 生成测试报告
function generateReport(results) {
  console.log('\n📊 测试报告');
  console.log('='.repeat(50));
  
  const totalTests = Object.keys(results).length;
  const passedTests = Object.values(results).filter(Boolean).length;
  const score = Math.round((passedTests / totalTests) * 100);
  
  console.log(`总体评分: ${score}% (${passedTests}/${totalTests})`);
  console.log('');
  
  Object.entries(results).forEach(([test, passed]) => {
    const status = passed ? '✅ 通过' : '❌ 失败';
    console.log(`${test}: ${status}`);
  });
  
  if (score === 100) {
    console.log('\n🎉 所有测试通过！字体配置已优化完成。');
  } else {
    console.log('\n⚠️  部分测试失败，请检查上述问题。');
  }
  
  return score;
}

// 主测试函数
function runTests() {
  console.log('🚀 开始字体一致性测试...\n');
  
  const results = {
    '字体文件检查': checkFontFiles(),
    '字体映射配置': checkFontMapping(),
    'next/font配置': checkNextFontConfig(),
    'Tailwind配置': checkTailwindConfig()
  };
  
  const score = generateReport(results);
  
  process.exit(score === 100 ? 0 : 1);
}

// 运行测试
if (require.main === module) {
  runTests();
}

module.exports = {
  runTests,
  checkFontFiles,
  checkFontMapping,
  checkNextFontConfig,
  checkTailwindConfig
};
