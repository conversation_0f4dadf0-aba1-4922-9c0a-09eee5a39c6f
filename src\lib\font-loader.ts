'use client';

import { PDFDocument, PDFFont } from 'pdf-lib';
import { FONT_MAPPING, getPdfFontKey } from './fonts';

/**
 * 字体加载器类
 * 负责从Google Fonts加载字体文件，与next/font集成
 */
export class FontLoader {
  private static fontCache: Map<string, ArrayBuffer> = new Map();
  private static fontUrlCache: Map<string, string> = new Map();

  /**
   * 直接从Google Fonts静态URL加载字体
   * 优化版本，支持动态URL获取和更好的缓存
   */
  private static async loadGoogleFont(fontFamily: string, weight: number = 400): Promise<ArrayBuffer | null> {
    // 使用统一的字体键名
    const normalizedFontFamily = getPdfFontKey(fontFamily);
    const cacheKey = `${normalizedFontFamily}-${weight}`;

    // 检查缓存
    if (this.fontCache.has(cacheKey)) {
      return this.fontCache.get(cacheKey)!;
    }

    try {
      // 更新的Google Fonts URL映射
      const fontUrls: Record<string, string> = {
        'Dancing Script-400': 'https://fonts.gstatic.com/s/dancingscript/v25/If2cXTr6YS-zF4S-kcSWSVi_sxjsohD9F50Ruu7BMSo3ROp-.woff2',
        'Playfair Display-400': 'https://fonts.gstatic.com/s/playfairdisplay/v36/nuFvD-vYSZviVYUb_rj3ij__anPXJzDwcbmjWBN2PKdFvXDXbtXK-F2qO0isEw.woff2',
        'Playfair Display-600': 'https://fonts.gstatic.com/s/playfairdisplay/v36/nuFvD-vYSZviVYUb_rj3ij__anPXJzDwcbmjWBN2PKdFvXDXbtXK-F2qC0msEw.woff2',
        'Playfair Display-700': 'https://fonts.gstatic.com/s/playfairdisplay/v36/nuFvD-vYSZviVYUb_rj3ij__anPXJzDwcbmjWBN2PKdFvXDXbtXK-F2qDUmsEw.woff2',
        'Inter-400': 'https://fonts.gstatic.com/s/inter/v18/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiJ-Ek-_EeA.woff2',
        'Inter-600': 'https://fonts.gstatic.com/s/inter/v18/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyYwZ9hiJ-Ek-_EeA.woff2',
        'Crimson Text-400': 'https://fonts.gstatic.com/s/crimsontext/v19/wlp2gwHKFkZgtmSR3NB0oRJvaAJSA_JN3Q.woff2',
        'Crimson Text-600': 'https://fonts.gstatic.com/s/crimsontext/v19/wlpzgwHKFkZgtmSR3NB0oRJX_QdYX_HQsAITq6Qjjw.woff2',
        'Crimson Text-700': 'https://fonts.gstatic.com/s/crimsontext/v19/wlpzgwHKFkZgtmSR3NB0oRJX_QdYX_HQsAITq6Qjjw.woff2',
        'Source Sans Pro-400': 'https://fonts.gstatic.com/s/sourcesanspro/v22/6xK3dSBYKcSV-LCoeQqfX1RYOo3qOK7lujVj9w.woff2',
        'Source Sans Pro-600': 'https://fonts.gstatic.com/s/sourcesanspro/v22/6xKydSBYKcSV-LCoeQqfX1RYOo3i54rwlxdu3cOWxw.woff2',
        'Source Sans Pro-700': 'https://fonts.gstatic.com/s/sourcesanspro/v22/6xKydSBYKcSV-LCoeQqfX1RYOo3ig4vwlxdu3cOWxw.woff2',
        'Great Vibes-400': 'https://fonts.gstatic.com/s/greatvibes/v16/RWmMoKWR9v4ksMfaWd_JN-XCg6UKDXlq.woff2'
      };

      const fontUrl = fontUrls[cacheKey];
      if (!fontUrl) {
        console.warn(`No font URL found for ${cacheKey}`);
        return null;
      }

      console.log(`Loading font: ${cacheKey} from ${fontUrl}`);

      const response = await fetch(fontUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      });

      if (!response.ok) {
        console.warn(`Failed to fetch font ${cacheKey}: ${response.status}`);
        return null;
      }

      const fontBytes = await response.arrayBuffer();

      // 缓存字体数据
      this.fontCache.set(cacheKey, fontBytes);

      return fontBytes;
    } catch (error) {
      console.warn(`Failed to load font ${cacheKey}:`, error);
      return null;
    }
  }

  /**
   * 批量加载字体
   */
  static async loadFonts(
    pdfDoc: PDFDocument,
    fontConfigs: Array<{ family: string; weight?: number }>
  ): Promise<Map<string, PDFFont>> {
    const fonts = new Map<string, PDFFont>();

    for (const { family, weight = 400 } of fontConfigs) {
      try {
        const fontBytes = await this.loadGoogleFont(family, weight);
        if (fontBytes) {
          const font = await pdfDoc.embedFont(fontBytes);
          fonts.set(`${family}-${weight}`, font);
          fonts.set(family, font); // 也用简单名称作为键
          console.log(`Successfully loaded font: ${family}-${weight}`);
        }
      } catch (error) {
        console.warn(`Failed to embed font ${family}-${weight}:`, error);
      }
    }

    return fonts;
  }

  /**
   * 清除字体缓存
   */
  static clearCache(): void {
    this.fontCache.clear();
  }

  /**
   * 获取缓存大小
   */
  static getCacheSize(): number {
    return this.fontCache.size;
  }
}
