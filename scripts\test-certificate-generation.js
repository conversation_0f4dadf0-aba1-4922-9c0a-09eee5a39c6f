#!/usr/bin/env node

/**
 * 证书生成功能测试脚本
 * 验证整个证书生成流程是否正常工作
 */

const fs = require('fs');
const path = require('path');

// 模拟测试数据
const testData = {
  recipientName: '<PERSON>',
  details: 'For outstanding achievement in web development and completing the advanced React.js course with excellence',
  date: 'December 25, 2024',
  signature: '<PERSON>, Director'
};

// 检查关键文件是否存在
function checkCriticalFiles() {
  console.log('🔍 检查关键文件...');
  
  const criticalFiles = [
    'src/app/layout.tsx',
    'src/lib/fonts.ts',
    'src/lib/font-loader.ts',
    'src/lib/pdf-generator.ts',
    'src/components/certificate/CertificatePreview.tsx',
    'src/lib/certificate-templates.ts'
  ];
  
  let allExist = true;
  
  criticalFiles.forEach(file => {
    const filePath = path.join(__dirname, '..', file);
    if (fs.existsSync(filePath)) {
      console.log(`✅ ${file}: 存在`);
    } else {
      console.log(`❌ ${file}: 不存在`);
      allExist = false;
    }
  });
  
  return allExist;
}

// 检查字体导入语法
function checkFontImports() {
  console.log('\n🔍 检查字体导入语法...');
  
  try {
    const layoutContent = fs.readFileSync(
      path.join(__dirname, '../src/app/layout.tsx'), 
      'utf8'
    );
    
    // 检查是否有语法错误的字体导入
    const problematicImports = [
      'Source_Sans_Pro', // 应该是 Source_Sans_3
    ];
    
    let hasProblems = false;
    
    problematicImports.forEach(importName => {
      if (layoutContent.includes(importName)) {
        console.log(`❌ 发现问题导入: ${importName}`);
        hasProblems = true;
      }
    });
    
    // 检查正确的导入
    const correctImports = [
      'Source_Sans_3',
      'Dancing_Script',
      'Playfair_Display',
      'Crimson_Text',
      'Great_Vibes'
    ];
    
    correctImports.forEach(importName => {
      if (layoutContent.includes(importName)) {
        console.log(`✅ 正确导入: ${importName}`);
      } else {
        console.log(`⚠️  缺少导入: ${importName}`);
      }
    });
    
    return !hasProblems;
  } catch (error) {
    console.error('❌ 读取layout.tsx失败:', error.message);
    return false;
  }
}

// 检查模板配置
function checkTemplateConfig() {
  console.log('\n🔍 检查模板配置...');
  
  try {
    const templatesContent = fs.readFileSync(
      path.join(__dirname, '../src/lib/certificate-templates.ts'), 
      'utf8'
    );
    
    // 检查是否包含基本模板
    const requiredElements = [
      'CERTIFICATE_TEMPLATES',
      'achievement-template-1',
      'completion-template-1',
      'layout: {',
      'name: {',
      'details: {',
      'date: {',
      'signature: {'
    ];
    
    let allPresent = true;
    
    requiredElements.forEach(element => {
      if (templatesContent.includes(element)) {
        console.log(`✅ ${element}: 已配置`);
      } else {
        console.log(`❌ ${element}: 未找到`);
        allPresent = false;
      }
    });
    
    return allPresent;
  } catch (error) {
    console.error('❌ 读取模板配置失败:', error.message);
    return false;
  }
}

// 检查PDF生成器配置
function checkPdfGeneratorConfig() {
  console.log('\n🔍 检查PDF生成器配置...');
  
  try {
    const pdfGenContent = fs.readFileSync(
      path.join(__dirname, '../src/lib/pdf-generator.ts'), 
      'utf8'
    );
    
    // 检查关键功能
    const requiredFeatures = [
      'class PDFGenerator',
      'loadFonts',
      'drawCertificate',
      'getFont',
      'FontLoader.loadFonts'
    ];
    
    let allPresent = true;
    
    requiredFeatures.forEach(feature => {
      if (pdfGenContent.includes(feature)) {
        console.log(`✅ ${feature}: 已实现`);
      } else {
        console.log(`❌ ${feature}: 未找到`);
        allPresent = false;
      }
    });
    
    return allPresent;
  } catch (error) {
    console.error('❌ 读取PDF生成器配置失败:', error.message);
    return false;
  }
}

// 检查预览组件配置
function checkPreviewComponent() {
  console.log('\n🔍 检查预览组件配置...');
  
  try {
    const previewContent = fs.readFileSync(
      path.join(__dirname, '../src/components/certificate/CertificatePreview.tsx'), 
      'utf8'
    );
    
    // 检查关键功能
    const requiredFeatures = [
      'CertificatePreview',
      'getFontStyle',
      'scaleFactor',
      'containerHeight',
      'template.layout.name',
      'optimizeLegibility'
    ];
    
    let allPresent = true;
    
    requiredFeatures.forEach(feature => {
      if (previewContent.includes(feature)) {
        console.log(`✅ ${feature}: 已实现`);
      } else {
        console.log(`❌ ${feature}: 未找到`);
        allPresent = false;
      }
    });
    
    return allPresent;
  } catch (error) {
    console.error('❌ 读取预览组件失败:', error.message);
    return false;
  }
}

// 生成测试报告
function generateTestReport(results) {
  console.log('\n📊 证书生成功能测试报告');
  console.log('='.repeat(50));
  
  const totalTests = Object.keys(results).length;
  const passedTests = Object.values(results).filter(Boolean).length;
  const score = Math.round((passedTests / totalTests) * 100);
  
  console.log(`总体评分: ${score}% (${passedTests}/${totalTests})`);
  console.log('');
  
  Object.entries(results).forEach(([test, passed]) => {
    const status = passed ? '✅ 通过' : '❌ 失败';
    console.log(`${test}: ${status}`);
  });
  
  if (score === 100) {
    console.log('\n🎉 所有测试通过！证书生成功能正常。');
    console.log('💡 建议：');
    console.log('   1. 在浏览器中访问 http://localhost:3001 测试界面');
    console.log('   2. 尝试生成不同模板的证书');
    console.log('   3. 验证预览与PDF的一致性');
  } else {
    console.log('\n⚠️  部分测试失败，请检查上述问题。');
    console.log('🔧 修复建议：');
    if (!results['字体导入检查']) {
      console.log('   - 检查 src/app/layout.tsx 中的字体导入语法');
    }
    if (!results['模板配置检查']) {
      console.log('   - 检查 src/lib/certificate-templates.ts 配置');
    }
    if (!results['PDF生成器检查']) {
      console.log('   - 检查 src/lib/pdf-generator.ts 实现');
    }
    if (!results['预览组件检查']) {
      console.log('   - 检查 src/components/certificate/CertificatePreview.tsx');
    }
  }
  
  return score;
}

// 主测试函数
function runCertificateTests() {
  console.log('🚀 开始证书生成功能测试...\n');
  
  const results = {
    '关键文件检查': checkCriticalFiles(),
    '字体导入检查': checkFontImports(),
    '模板配置检查': checkTemplateConfig(),
    'PDF生成器检查': checkPdfGeneratorConfig(),
    '预览组件检查': checkPreviewComponent()
  };
  
  const score = generateTestReport(results);
  
  process.exit(score === 100 ? 0 : 1);
}

// 运行测试
if (require.main === module) {
  runCertificateTests();
}

module.exports = {
  runCertificateTests,
  checkCriticalFiles,
  checkFontImports,
  checkTemplateConfig,
  checkPdfGeneratorConfig,
  checkPreviewComponent
};
